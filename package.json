{"name": "bunwindmill", "module": "index.ts", "type": "module", "private": true, "scripts": {"arbitrage-manager": "bun run u/tools/arbitrage-manager.ts", "arbitrage-recovery": "bun run u/libs/arbitrage-recovery-example.ts", "arbitrage-daemon": "bun run u/libs/arbitrage-recovery-example.ts --daemon"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"ioredis": "^5.7.0", "postgres": "^3.4.7"}}