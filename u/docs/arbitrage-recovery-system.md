# 套利流程恢复系统

## 概述

基于Redis的套利流程恢复系统，可以处理套利过程中的中断和失败，支持自动恢复和断点续传。

## 核心特性

1. **流程状态持久化**: 每个套利流程的状态都保存在Redis中
2. **断点续传**: 支持从任意步骤恢复执行
3. **自动重试**: 失败的步骤会自动重试，最多重试3次
4. **超时处理**: 超过2小时的流程会被标记为超时失败
5. **活跃流程管理**: 维护活跃流程列表，便于监控和恢复

## 流程步骤定义

```typescript
enum ArbitrageStep {
  INIT = "init",                    // 初始化
  BUY_TOKEN = "buy_token",          // 购买代币
  WITHDRAW_TOKEN = "withdraw_token", // 提现代币
  SWAP_TOKEN = "swap_token",        // 交换代币
  BRIDGE_TRANSFER = "bridge_transfer", // 跨链转账
  UNWRAP_SOL = "unwrap_sol",        // 解包SOL (仅CEX->DEX)
  TRANSFER_TO_CEX = "transfer_to_cex", // 转账到CEX
  SELL_TOKEN = "sell_token",        // 卖出代币
  COMPLETED = "completed",          // 完成
  FAILED = "failed"                 // 失败
}
```

## Redis数据结构

### 1. 流程状态存储

**键**: `arbitrage:{processId}`
**类型**: String (JSON)
**过期时间**: 24小时

```json
{
  "id": "1703123456789_abc123def",
  "strategy": "cex_to_dex",
  "currentStep": "swap_token",
  "usdtAmount": 100,
  "createdAt": 1703123456789,
  "updatedAt": 1703123456890,
  "data": {
    "buyOrder": { /* 买单信息 */ },
    "swapResult": { /* 交换结果 */ },
    "bridgeResult": { /* 跨链结果 */ },
    "initialBalances": { /* 初始余额 */ },
    "finalBalances": { /* 最终余额 */ }
  },
  "error": "网络连接超时",
  "retryCount": 1,
  "maxRetries": 3
}
```

### 2. 活跃流程列表

**键**: `arbitrage:active_processes`
**类型**: Set
**内容**: 活跃流程的ID列表

## 使用方法

### 1. 基本使用

```typescript
import { ArbitrageService } from "./arbitrage";

const arbitrageService = new ArbitrageService(/* 配置 */);

// 检查并恢复未完成的流程
await arbitrageService.checkAndRecoverProcesses();

// 执行新的套利
try {
  const result = await arbitrageService.execIotexPath(100);
  console.log("套利完成:", result);
} catch (error) {
  console.log("流程已保存，可稍后恢复");
}
```

### 2. 守护进程模式

```typescript
// 每5分钟检查一次未完成的流程
setInterval(async () => {
  await arbitrageService.checkAndRecoverProcesses();
}, 5 * 60 * 1000);
```

## 恢复逻辑

### CEX -> DEX 流程恢复

1. **BUY_TOKEN**: 检查是否已有买单记录，没有则重新买入
2. **WITHDRAW_TOKEN**: 重新提现到IoTeX钱包
3. **SWAP_TOKEN**: 重新在MIMO上交换代币
4. **BRIDGE_TRANSFER**: 重新执行跨链转账
5. **UNWRAP_SOL**: 重新解包wSOL
6. **TRANSFER_TO_CEX**: 重新转账到CEX
7. **SELL_TOKEN**: 重新卖出代币

### DEX -> CEX 流程恢复

1. **BUY_TOKEN**: 检查是否已有买单记录，没有则重新买入
2. **WITHDRAW_TOKEN**: 重新提现到Solana钱包
3. **BRIDGE_TRANSFER**: 重新执行跨链转账
4. **SWAP_TOKEN**: 重新在MIMO上交换代币
5. **TRANSFER_TO_CEX**: 重新转账到CEX
6. **SELL_TOKEN**: 重新卖出代币

## 错误处理

1. **网络错误**: 自动重试，最多3次
2. **余额不足**: 标记为失败，需要人工干预
3. **API限制**: 等待后重试
4. **超时**: 超过2小时标记为失败

## 监控和维护

### 查看活跃流程

```bash
redis-cli SMEMBERS arbitrage:active_processes
```

### 查看特定流程状态

```bash
redis-cli GET arbitrage:{processId}
```

### 清理过期流程

系统会自动清理24小时过期的流程状态，但活跃流程列表需要定期清理：

```typescript
// 清理已完成或失败的流程
await arbitrageService.checkAndRecoverProcesses();
```

## 注意事项

1. **幂等性**: 所有恢复操作都是幂等的，重复执行不会造成问题
2. **资金安全**: 恢复前会检查余额状态，避免重复操作
3. **网络依赖**: 恢复过程依赖网络连接，确保网络稳定
4. **Redis可用性**: 系统依赖Redis存储状态，确保Redis服务稳定

## 部署建议

1. **独立监控进程**: 运行独立的恢复守护进程
2. **告警机制**: 对失败的流程设置告警
3. **日志记录**: 详细记录恢复过程的日志
4. **备份策略**: 定期备份Redis数据
