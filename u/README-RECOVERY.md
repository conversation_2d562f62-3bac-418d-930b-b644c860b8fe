# 套利流程恢复系统

## 🎯 解决的问题

原有的套利逻辑存在一个关键问题：**如果中途某一步失败了，就没办法恢复执行**。这个恢复系统通过Redis状态管理解决了这个问题。

## 🚀 核心特性

- ✅ **断点续传**: 从任意失败步骤恢复执行
- ✅ **自动重试**: 失败步骤自动重试，最多3次
- ✅ **超时处理**: 超过2小时的流程自动标记为失败
- ✅ **状态持久化**: 所有流程状态保存在Redis中
- ✅ **监控管理**: 提供完整的管理工具

## 📋 流程步骤

### CEX -> DEX 策略
1. `BUY_TOKEN` - 在CEX购买IOTX
2. `WITHDRAW_TOKEN` - 提现IOTX到IoTeX钱包
3. `SWAP_TOKEN` - 在MIMO将IOTX换成SOL
4. `BRIDGE_TRANSFER` - 跨链转移SOL到Solana
5. `UNWRAP_SOL` - 解包wSOL
6. `TRANSFER_TO_CEX` - 转账到CEX
7. `SELL_TOKEN` - 卖出SOL
8. `COMPLETED` - 完成

### DEX -> CEX 策略
1. `BUY_TOKEN` - 在CEX购买SOL
2. `WITHDRAW_TOKEN` - 提现SOL到Solana钱包
3. `BRIDGE_TRANSFER` - 跨链转移SOL到IoTeX
4. `SWAP_TOKEN` - 在MIMO将SOL换成IOTX
5. `TRANSFER_TO_CEX` - 转账IOTX到CEX
6. `SELL_TOKEN` - 卖出IOTX
7. `COMPLETED` - 完成

## 🛠️ 使用方法

### 1. 基本使用

```typescript
import { ArbitrageService } from "./libs/arbitrage";

const arbitrageService = new ArbitrageService(/* 配置 */);

// 启动时检查未完成的流程
await arbitrageService.checkAndRecoverProcesses();

// 执行套利
try {
  const result = await arbitrageService.execIotexPath(100);
  console.log("套利完成:", result);
} catch (error) {
  console.log("流程已保存，可稍后恢复");
}
```

### 2. 运行恢复示例

```bash
# 运行恢复示例
bun run arbitrage-recovery

# 运行守护进程模式（每5分钟检查一次）
bun run arbitrage-daemon
```

### 3. 管理工具

```bash
# 查看流程状态摘要
bun run arbitrage-manager summary

# 列出所有活跃流程
bun run arbitrage-manager list

# 清理已完成和超时的流程
bun run arbitrage-manager cleanup

# 删除特定流程
bun run arbitrage-manager delete <process-id>

# 标记流程为失败
bun run arbitrage-manager fail <process-id> "原因"
```

## 🔧 配置说明

### Redis配置
系统使用现有的Redis连接配置（`u/libs/kv.ts`）：

```typescript
import { redisClient } from "./kv";
```

### 流程配置
每个流程支持以下配置：

```typescript
interface ArbitrageState {
  id: string;           // 唯一流程ID
  strategy: string;     // 策略类型
  currentStep: string;  // 当前步骤
  usdtAmount: number;   // 交易金额
  retryCount: number;   // 重试次数
  maxRetries: number;   // 最大重试次数（默认3次）
}
```

## 📊 监控和维护

### 查看Redis中的数据

```bash
# 查看活跃流程列表
redis-cli SMEMBERS arbitrage:active_processes

# 查看特定流程状态
redis-cli GET arbitrage:<process-id>

# 查看所有套利相关键
redis-cli KEYS arbitrage:*
```

### 定期维护

建议设置定期任务：

```bash
# 每小时清理一次已完成的流程
0 * * * * cd /path/to/project && bun run arbitrage-manager cleanup-completed

# 每天清理一次超时流程
0 0 * * * cd /path/to/project && bun run arbitrage-manager cleanup-timeout
```

## 🚨 故障处理

### 常见问题

1. **网络连接失败**
   - 系统会自动重试，最多3次
   - 可以手动触发恢复：`bun run arbitrage-recovery`

2. **余额不足**
   - 流程会标记为失败，需要人工检查
   - 使用管理工具查看详情：`bun run arbitrage-manager summary`

3. **API限制**
   - 系统会等待后重试
   - 可以调整重试间隔和次数

4. **流程卡住**
   - 超过2小时会自动标记为超时
   - 可以手动标记为失败：`bun run arbitrage-manager fail <id> "卡住"`

### 紧急处理

```bash
# 查看所有问题流程
bun run arbitrage-manager summary

# 强制清理所有流程
bun run arbitrage-manager cleanup

# 手动标记问题流程为失败
bun run arbitrage-manager fail <process-id> "紧急处理"
```

## 📈 最佳实践

1. **启动检查**: 每次启动应用时先检查未完成流程
2. **守护进程**: 运行独立的恢复守护进程
3. **监控告警**: 对失败流程设置告警机制
4. **定期清理**: 定期清理已完成和超时的流程
5. **日志记录**: 保留详细的执行日志

## 🔒 安全考虑

1. **幂等性**: 所有恢复操作都是幂等的
2. **资金安全**: 恢复前检查余额状态
3. **重复执行**: 避免重复操作造成损失
4. **权限控制**: 确保Redis访问权限安全

## 📝 开发说明

### 添加新步骤

1. 在`ArbitrageStep`枚举中添加新步骤
2. 在恢复方法中添加对应的处理逻辑
3. 在执行方法中添加状态更新调用

### 扩展功能

- 可以添加更多的重试策略
- 支持不同类型的错误处理
- 添加更详细的监控指标
- 支持流程暂停和恢复

这个恢复系统确保了套利流程的可靠性和连续性，即使在网络故障或系统重启的情况下也能正常恢复执行。
