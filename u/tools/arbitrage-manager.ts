import { redisClient } from "../libs/kv";

/**
 * 套利流程管理工具
 * 用于查看、管理和清理套利流程状态
 */

interface ArbitrageState {
  id: string;
  strategy: string;
  currentStep: string;
  usdtAmount: number;
  createdAt: number;
  updatedAt: number;
  data: any;
  error?: string;
  retryCount: number;
  maxRetries: number;
}

class ArbitrageManager {
  private readonly REDIS_PREFIX = "arbitrage:";
  private readonly ACTIVE_PROCESSES_KEY = "arbitrage:active_processes";

  /**
   * 获取所有活跃流程
   */
  async getActiveProcesses(): Promise<string[]> {
    return await redisClient.smembers(this.ACTIVE_PROCESSES_KEY);
  }

  /**
   * 获取流程状态
   */
  async getProcessState(processId: string): Promise<ArbitrageState | null> {
    const key = `${this.REDIS_PREFIX}${processId}`;
    const data = await redisClient.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 获取所有流程的详细信息
   */
  async getAllProcessDetails(): Promise<ArbitrageState[]> {
    const processIds = await this.getActiveProcesses();
    const processes: ArbitrageState[] = [];

    for (const id of processIds) {
      const state = await this.getProcessState(id);
      if (state) {
        processes.push(state);
      }
    }

    return processes.sort((a, b) => b.createdAt - a.createdAt);
  }

  /**
   * 删除流程
   */
  async deleteProcess(processId: string): Promise<void> {
    const key = `${this.REDIS_PREFIX}${processId}`;
    await redisClient.del(key);
    await redisClient.srem(this.ACTIVE_PROCESSES_KEY, processId);
    console.log(`✅ 已删除流程: ${processId}`);
  }

  /**
   * 清理已完成或失败的流程
   */
  async cleanupCompletedProcesses(): Promise<void> {
    const processes = await this.getAllProcessDetails();
    let cleanedCount = 0;

    for (const process of processes) {
      if (process.currentStep === "completed" || process.currentStep === "failed") {
        await this.deleteProcess(process.id);
        cleanedCount++;
      }
    }

    console.log(`🧹 清理了 ${cleanedCount} 个已完成/失败的流程`);
  }

  /**
   * 清理超时的流程
   */
  async cleanupTimeoutProcesses(timeoutHours: number = 2): Promise<void> {
    const processes = await this.getAllProcessDetails();
    const timeoutMs = timeoutHours * 60 * 60 * 1000;
    const now = Date.now();
    let cleanedCount = 0;

    for (const process of processes) {
      if (now - process.updatedAt > timeoutMs) {
        await this.deleteProcess(process.id);
        cleanedCount++;
      }
    }

    console.log(`⏰ 清理了 ${cleanedCount} 个超时流程`);
  }

  /**
   * 显示流程状态摘要
   */
  async showSummary(): Promise<void> {
    const processes = await this.getAllProcessDetails();
    
    console.log("\n📊 套利流程状态摘要");
    console.log("=" .repeat(50));
    
    if (processes.length === 0) {
      console.log("✅ 没有活跃的流程");
      return;
    }

    // 按状态分组
    const statusGroups: { [key: string]: ArbitrageState[] } = {};
    processes.forEach(p => {
      if (!statusGroups[p.currentStep]) {
        statusGroups[p.currentStep] = [];
      }
      statusGroups[p.currentStep].push(p);
    });

    // 显示统计
    console.log(`总流程数: ${processes.length}`);
    Object.entries(statusGroups).forEach(([status, procs]) => {
      console.log(`  ${status}: ${procs.length} 个`);
    });

    console.log("\n📋 详细信息:");
    processes.forEach(p => {
      const age = Math.floor((Date.now() - p.createdAt) / 1000 / 60); // 分钟
      const lastUpdate = Math.floor((Date.now() - p.updatedAt) / 1000 / 60); // 分钟
      
      console.log(`\n🔹 ${p.id}`);
      console.log(`   策略: ${p.strategy}`);
      console.log(`   步骤: ${p.currentStep}`);
      console.log(`   金额: $${p.usdtAmount}`);
      console.log(`   创建: ${age} 分钟前`);
      console.log(`   更新: ${lastUpdate} 分钟前`);
      console.log(`   重试: ${p.retryCount}/${p.maxRetries}`);
      
      if (p.error) {
        console.log(`   错误: ${p.error}`);
      }
    });
  }

  /**
   * 强制标记流程为失败
   */
  async markAsFailed(processId: string, reason: string = "手动标记"): Promise<void> {
    const state = await this.getProcessState(processId);
    if (!state) {
      console.log(`❌ 流程 ${processId} 不存在`);
      return;
    }

    state.currentStep = "failed";
    state.error = reason;
    state.updatedAt = Date.now();

    const key = `${this.REDIS_PREFIX}${processId}`;
    await redisClient.setex(key, 86400, JSON.stringify(state));
    
    console.log(`✅ 已将流程 ${processId} 标记为失败`);
  }
}

// 命令行工具
async function main() {
  const manager = new ArbitrageManager();
  const command = process.argv[2];
  const arg = process.argv[3];

  try {
    switch (command) {
      case "summary":
      case "status":
        await manager.showSummary();
        break;

      case "cleanup":
        await manager.cleanupCompletedProcesses();
        await manager.cleanupTimeoutProcesses();
        break;

      case "cleanup-completed":
        await manager.cleanupCompletedProcesses();
        break;

      case "cleanup-timeout":
        const hours = arg ? parseInt(arg) : 2;
        await manager.cleanupTimeoutProcesses(hours);
        break;

      case "delete":
        if (!arg) {
          console.log("❌ 请提供流程ID");
          process.exit(1);
        }
        await manager.deleteProcess(arg);
        break;

      case "fail":
        if (!arg) {
          console.log("❌ 请提供流程ID");
          process.exit(1);
        }
        const reason = process.argv[4] || "手动标记";
        await manager.markAsFailed(arg, reason);
        break;

      case "list":
        const processes = await manager.getActiveProcesses();
        console.log("活跃流程ID:");
        processes.forEach(id => console.log(`  ${id}`));
        break;

      default:
        console.log(`
套利流程管理工具

使用方法:
  npm run arbitrage-manager <command> [args]

命令:
  summary              显示流程状态摘要
  list                 列出所有活跃流程ID
  cleanup              清理已完成和超时的流程
  cleanup-completed    只清理已完成的流程
  cleanup-timeout [h]  清理超时流程 (默认2小时)
  delete <id>          删除指定流程
  fail <id> [reason]   标记流程为失败

示例:
  npm run arbitrage-manager summary
  npm run arbitrage-manager delete 1703123456789_abc123def
  npm run arbitrage-manager fail 1703123456789_abc123def "网络问题"
        `);
        break;
    }
  } catch (error) {
    console.error("❌ 执行失败:", error);
    process.exit(1);
  } finally {
    await redisClient.quit();
  }
}

if (require.main === module) {
  main();
}

export { ArbitrageManager };
