import { PostgresAdapter, type PostgresAdapterConfig } from "./postgres.ts";

export interface TaskEntity {
  id ?: string;
  name: string;
  description: string;
  created_at?: Date;
  updated_at?: Date;

  [key: string]: any;
}

export class ProjectAdapter extends PostgresAdapter<TaskEntity> {
  constructor(config: Omit<PostgresAdapterConfig, 'tableName'> = {}) {
    super({
      ...config,
      tableName: 'projects'
    });
  }
}
