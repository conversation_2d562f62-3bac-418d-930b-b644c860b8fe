import { ArbitrageService } from "./libs/arbitrage";
import { CexService } from "./libs/cex";
import { MimoService } from "./libs/mimo";
import { IoTubeBridgeService } from "./libs/iotube";
import { SolanaService } from "./libs/solana";

/**
 * 套利恢复系统使用示例
 * 
 * 现在执行套利时会自动检查和恢复未完成的流程
 * 无需额外的管理脚本或守护进程
 */

async function main() {
  // 初始化服务
  const cexService = new CexService({
    // CEX 配置
  });

  const mimoService = new MimoService({
    // MIMO 配置  
  });

  const bridgeService = new IoTubeBridgeService({
    // Bridge 配置
  });

  const solanaService = new SolanaService({
    // Solana 配置
  });

  // 创建套利服务
  const arbitrageService = new ArbitrageService(
    cexService,
    mimoService, 
    bridgeService,
    solanaService,
    {
      maxTradeAmount: 1000,
      walletAddresses: {
        iotex: "your-iotex-address",
        solana: "your-solana-address"
      }
    }
  );

  // 直接执行套利 - 会自动检查和恢复未完成的流程
  try {
    console.log("🚀 开始执行套利...");
    
    // 执行 CEX -> DEX 套利
    const result1 = await arbitrageService.execIotexPath(100);
    console.log("✅ CEX -> DEX 套利完成:", result1.profitAnalysis);

    // 执行 DEX -> CEX 套利  
    const result2 = await arbitrageService.execSolPath(100);
    console.log("✅ DEX -> CEX 套利完成:", result2.profitAnalysis);

  } catch (error) {
    console.error("❌ 套利执行失败:", error);
    console.log("💡 流程状态已保存，下次执行时会自动恢复");
  }
}

// 运行示例
main().catch(console.error);
