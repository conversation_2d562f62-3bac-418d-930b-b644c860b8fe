import { ArbitrageService } from "./arbitrage";
import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube";
import { SolanaService } from "./solana";

/**
 * 套利恢复系统使用示例
 * 
 * 这个示例展示了如何使用基于Redis的套利流程恢复系统
 */

async function main() {
  // 初始化各个服务
  const cexService = new CexService({
    // CEX 配置
  });

  const mimoService = new MimoService({
    // MIMO 配置
  });

  const bridgeService = new IoTubeBridgeService({
    // Bridge 配置
  });

  const solanaService = new SolanaService({
    // Solana 配置
  });

  // 创建套利服务实例
  const arbitrageService = new ArbitrageService(
    cexService,
    mimoService,
    bridgeService,
    solanaService,
    {
      maxTradeAmount: 1000,
      walletAddresses: {
        iotex: "your-iotex-address",
        solana: "your-solana-address"
      }
    }
  );

  console.log("🚀 启动套利恢复系统");

  // 1. 首先检查并恢复任何未完成的流程
  console.log("📋 检查未完成的流程...");
  await arbitrageService.checkAndRecoverProcesses();

  // 2. 执行新的套利策略（如果需要）
  // 注意：在实际使用中，你应该根据市场条件决定是否执行新的套利
  
  // 示例：执行 CEX -> DEX 套利
  try {
    console.log("💰 执行新的 CEX -> DEX 套利...");
    const result = await arbitrageService.execIotexPath(100); // $100 USDT
    console.log("✅ 套利完成:", result);
  } catch (error) {
    console.error("❌ 套利执行失败:", error);
    console.log("💡 流程状态已保存到Redis，可以稍后恢复");
  }

  // 3. 再次检查流程状态（可选）
  console.log("🔍 最终检查流程状态...");
  await arbitrageService.checkAndRecoverProcesses();
}

/**
 * 定时检查和恢复流程的示例
 * 可以作为独立的监控进程运行
 */
async function recoveryDaemon() {
  const arbitrageService = new ArbitrageService(
    new CexService({}),
    new MimoService({}),
    new IoTubeBridgeService({}),
    new SolanaService({}),
    {
      maxTradeAmount: 1000,
      walletAddresses: {
        iotex: "your-iotex-address",
        solana: "your-solana-address"
      }
    }
  );

  console.log("🔄 启动套利恢复守护进程");

  // 每5分钟检查一次未完成的流程
  setInterval(async () => {
    try {
      console.log("⏰ 定时检查未完成的流程...");
      await arbitrageService.checkAndRecoverProcesses();
    } catch (error) {
      console.error("❌ 恢复检查失败:", error);
    }
  }, 5 * 60 * 1000); // 5分钟

  // 保持进程运行
  process.on('SIGINT', () => {
    console.log("🛑 收到停止信号，退出守护进程");
    process.exit(0);
  });
}

// 根据命令行参数决定运行模式
if (process.argv.includes('--daemon')) {
  recoveryDaemon();
} else {
  main().catch(console.error);
}
