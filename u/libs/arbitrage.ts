import { CexService } from "./cex";
import { MimoService } from "./mimo";
import { IoTubeBridgeService } from "./iotube";
import { SolanaService } from "./solana";
import { redisClient } from "./kv";
// @ts-ignore
import { from } from "@iotexproject/iotex-address-ts";

// 定义套利流程步骤枚举
enum ArbitrageStep {
  INIT = "init",
  BUY_TOKEN = "buy_token",
  WITHDRAW_TOKEN = "withdraw_token",
  SWAP_TOKEN = "swap_token",
  BRIDGE_TRANSFER = "bridge_transfer",
  UNWRAP_SOL = "unwrap_sol",
  TRANSFER_TO_CEX = "transfer_to_cex",
  SELL_TOKEN = "sell_token",
  COMPLETED = "completed",
  FAILED = "failed"
}

// 定义套利策略类型
enum ArbitrageStrategy {
  CEX_TO_DEX = "cex_to_dex",
  DEX_TO_CEX = "dex_to_cex"
}

// 定义流程状态接口
interface ArbitrageState {
  id: string;
  strategy: ArbitrageStrategy;
  currentStep: ArbitrageStep;
  usdtAmount: number;
  createdAt: number;
  updatedAt: number;
  data: {
    buyOrder?: any;
    swapResult?: any;
    bridgeResult?: any;
    sellOrder?: any;
    initialBalances?: any;
    finalBalances?: any;
    [key: string]: any;
  };
  error?: string;
  retryCount: number;
  maxRetries: number;
}

/**
 * 套利策略模块
 * 协调 CEX、MIMO 和 IoTube Bridge 服务执行套利策略
 * 支持流程恢复和断点续传
 */
export class ArbitrageService {
  private cexService: CexService;
  private mimoService: MimoService;
  private bridgeService: IoTubeBridgeService;
  private solanaService: SolanaService;
  private config: {
    maxTradeAmount: number;
    walletAddresses: {
      iotex: string;
      solana: string;
    };
  };

  // Redis 键前缀
  private readonly REDIS_PREFIX = "arbitrage:";
  private readonly ACTIVE_PROCESSES_KEY = "arbitrage:active_processes";

  constructor(
    cexService: CexService,
    mimoService: MimoService,
    bridgeService: IoTubeBridgeService,
    solanaService: SolanaService,
    config: {
      maxTradeAmount: number;
      walletAddresses: {
        iotex: string;
        solana: string;
      };
    }
  ) {
    this.cexService = cexService;
    this.mimoService = mimoService;
    this.bridgeService = bridgeService;
    this.solanaService = solanaService;
    this.config = config;
  }

  /**
   * 生成唯一的流程ID
   */
  private generateProcessId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 保存流程状态到Redis
   */
  private async saveState(state: ArbitrageState): Promise<void> {
    const key = `${this.REDIS_PREFIX}${state.id}`;
    state.updatedAt = Date.now();
    await redisClient.setex(key, 86400, JSON.stringify(state)); // 24小时过期
    await redisClient.sadd(this.ACTIVE_PROCESSES_KEY, state.id);
  }

  /**
   * 从Redis获取流程状态
   */
  private async getState(processId: string): Promise<ArbitrageState | null> {
    const key = `${this.REDIS_PREFIX}${processId}`;
    const data = await redisClient.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 删除流程状态
   */
  private async deleteState(processId: string): Promise<void> {
    const key = `${this.REDIS_PREFIX}${processId}`;
    await redisClient.del(key);
    await redisClient.srem(this.ACTIVE_PROCESSES_KEY, processId);
  }

  /**
   * 获取所有活跃的流程ID
   */
  private async getActiveProcesses(): Promise<string[]> {
    return await redisClient.smembers(this.ACTIVE_PROCESSES_KEY);
  }

  /**
   * 更新流程步骤
   */
  private async updateStep(processId: string, step: ArbitrageStep, data?: any, error?: string): Promise<void> {
    const state = await this.getState(processId);
    if (!state) {
      throw new Error(`Process ${processId} not found`);
    }

    state.currentStep = step;
    if (data) {
      state.data = { ...state.data, ...data };
    }
    if (error) {
      state.error = error;
      state.retryCount++;
    }

    await this.saveState(state);
  }

  /**
   * 检查并恢复未完成的流程
   */
  async checkAndRecoverProcesses(): Promise<void> {
    try {
      const activeProcesses = await this.getActiveProcesses();
      if (activeProcesses.length === 0) return;

      console.log(`🔄 发现 ${activeProcesses.length} 个未完成流程，开始恢复...`);

      for (const processId of activeProcesses) {
        const state = await this.getState(processId);
        if (!state) {
          await redisClient.srem(this.ACTIVE_PROCESSES_KEY, processId);
          continue;
        }

        // 清理已完成或失败的流程
        if (state.currentStep === ArbitrageStep.COMPLETED || state.currentStep === ArbitrageStep.FAILED) {
          await redisClient.srem(this.ACTIVE_PROCESSES_KEY, processId);
          continue;
        }

        // 检查超时或重试次数
        const isTimeout = Date.now() - state.updatedAt > 2 * 60 * 60 * 1000;
        if (isTimeout || state.retryCount >= state.maxRetries) {
          await this.updateStep(processId, ArbitrageStep.FAILED, null, isTimeout ? "超时" : "重试次数超限");
          continue;
        }

        // 恢复流程
        console.log(`🔄 恢复流程 ${processId.slice(-8)}... (步骤: ${state.currentStep})`);
        try {
          await this.resumeProcess(state);
          console.log(`✅ 流程 ${processId.slice(-8)}... 恢复完成`);
        } catch (error) {
          console.error(`❌ 流程 ${processId.slice(-8)}... 恢复失败:`, error);
          await this.updateStep(processId, state.currentStep, null, error instanceof Error ? error.message : String(error));
        }
      }
    } catch (error) {
      console.error("检查恢复流程时出错:", error);
    }
  }

  /**
   * 恢复流程执行
   */
  private async resumeProcess(state: ArbitrageState): Promise<void> {
    console.log(`🔄 恢复流程 ${state.id}，策略: ${state.strategy}，当前步骤: ${state.currentStep}`);

    if (state.strategy === ArbitrageStrategy.CEX_TO_DEX) {
      await this.resumeIotexPath(state);
    } else if (state.strategy === ArbitrageStrategy.DEX_TO_CEX) {
      await this.resumeSolPath(state);
    } else {
      throw new Error(`Unknown strategy: ${state.strategy}`);
    }
  }

  /**
   * 恢复 CEX -> DEX 流程
   */
  private async resumeIotexPath(state: ArbitrageState): Promise<void> {
    switch (state.currentStep) {
      case ArbitrageStep.BUY_TOKEN:
        // 检查是否已经买入成功
        if (!state.data.buyOrder) {
          console.log("🔄 重新执行: 在 CEX 上买 IOTX");
          const buyOrder = await this.cexService.buyIotx(state.usdtAmount);
          await this.updateStep(state.id, ArbitrageStep.WITHDRAW_TOKEN, { buyOrder });
        } else {
          await this.updateStep(state.id, ArbitrageStep.WITHDRAW_TOKEN);
        }
        break;

      case ArbitrageStep.WITHDRAW_TOKEN:
        console.log("🔄 重新执行: 将 IOTX 提现到 IoTeX 钱包");
        const iotxAmount = Math.floor(state.data.buyOrder.filled || state.data.buyOrder.amount);
        await this.cexService.withdrawIotx(iotxAmount, this.config.walletAddresses.iotex);
        await this.waitForIotxBalanceIncrease(iotxAmount);
        await this.updateStep(state.id, ArbitrageStep.SWAP_TOKEN);
        break;

      case ArbitrageStep.SWAP_TOKEN:
        console.log("🔄 重新执行: 在 MIMO 将 IOTX 换成 SOL");
        const iotxAmountForSwap = Math.floor(state.data.buyOrder.filled || state.data.buyOrder.amount);
        const swapResult = await this.mimoService.swapIotxToSol(iotxAmountForSwap);
        await this.updateStep(state.id, ArbitrageStep.BRIDGE_TRANSFER, { swapResult });
        break;

      case ArbitrageStep.BRIDGE_TRANSFER:
        console.log("🔄 重新执行: 将 SOL 通过跨链桥转移到 Solana");
        const bridgeResult = await this.bridgeService.transferSolFromIotexToSolana(
          state.data.swapResult.solAmount,
          this.config.walletAddresses.solana
        );
        await this.waitForSolanaWsolBalanceIncrease(bridgeResult.receivedAmount, 1000 * 60 * 10);
        await this.updateStep(state.id, ArbitrageStep.UNWRAP_SOL, { bridgeResult });
        break;

      case ArbitrageStep.UNWRAP_SOL:
        console.log("🔄 重新执行: 解包 wSOL 并转账到 CEX");
        const solanaService = new SolanaService({
          rpcUrl: this.bridgeService.config.solanaRpcUrl,
          privateKey: this.bridgeService.config.solanaPrivateKey,
        });
        await solanaService.unwrapSol();
        await this.updateStep(state.id, ArbitrageStep.TRANSFER_TO_CEX);
        break;

      case ArbitrageStep.TRANSFER_TO_CEX:
        console.log("🔄 重新执行: 转账到 CEX");
        const solanaServiceForTransfer = new SolanaService({
          rpcUrl: this.bridgeService.config.solanaRpcUrl,
          privateKey: this.bridgeService.config.solanaPrivateKey,
        });
        const bnDepositSolAddress = await this.cexService.getSolDepositAddress();
        const transferTx = await solanaServiceForTransfer.transfer(bnDepositSolAddress, state.data.swapResult.solAmount);
        console.log(`💸 SOL 转账哈希: ${transferTx}`);
        await this.cexService.waitForSolBalanceIncrease(state.data.swapResult.solAmount);
        await this.updateStep(state.id, ArbitrageStep.SELL_TOKEN);
        break;

      case ArbitrageStep.SELL_TOKEN:
        console.log("🔄 重新执行: 在 CEX 卖出 SOL");
        await this.cexService.sellSymbol("SOLUSDT", state.data.swapResult.solAmount);
        await this.updateStep(state.id, ArbitrageStep.COMPLETED);
        break;

      default:
        throw new Error(`Unknown step for CEX->DEX strategy: ${state.currentStep}`);
    }

    // 如果还没完成，继续下一步
    const updatedState = await this.getState(state.id);
    if (updatedState && updatedState.currentStep !== ArbitrageStep.COMPLETED) {
      await this.resumeIotexPath(updatedState);
    }
  }

  /**
   * 恢复 DEX -> CEX 流程
   */
  private async resumeSolPath(state: ArbitrageState): Promise<void> {
    switch (state.currentStep) {
      case ArbitrageStep.BUY_TOKEN:
        // 检查是否已经买入成功
        if (!state.data.buyOrder) {
          console.log("🔄 重新执行: 在 CEX 上买 SOL");
          const buyOrder = await this.cexService.buySol(state.usdtAmount);
          await this.updateStep(state.id, ArbitrageStep.WITHDRAW_TOKEN, { buyOrder });
        } else {
          await this.updateStep(state.id, ArbitrageStep.WITHDRAW_TOKEN);
        }
        break;

      case ArbitrageStep.WITHDRAW_TOKEN:
        console.log("🔄 重新执行: 将 SOL 提现到 Solana 钱包");
        const solAmount = state.data.buyOrder.filled || state.data.buyOrder.amount;
        await this.cexService.withdrawSol(solAmount, this.config.walletAddresses.solana);
        await this.waitForSolanaBalanceIncrease(solAmount);
        await this.updateStep(state.id, ArbitrageStep.BRIDGE_TRANSFER);
        break;

      case ArbitrageStep.BRIDGE_TRANSFER:
        console.log("🔄 重新执行: 通过跨链桥将 SOL 转移到 IoTeX");
        const solAmountForBridge = state.data.buyOrder.filled || state.data.buyOrder.amount;
        const bridgeResult = await this.bridgeService.transferSolFromSolanaToIotex(
          solAmountForBridge,
          this.config.walletAddresses.iotex
        );
        await this.waitForSolBalanceIncrease(bridgeResult.receivedAmount);
        await this.updateStep(state.id, ArbitrageStep.SWAP_TOKEN, { bridgeResult });
        break;

      case ArbitrageStep.SWAP_TOKEN:
        console.log("🔄 重新执行: 在 MIMO 上将 SOL 转换为 IOTX");
        const swapResult = await this.mimoService.swapSolToIotx(state.data.bridgeResult.receivedAmount);
        await this.updateStep(state.id, ArbitrageStep.TRANSFER_TO_CEX, { swapResult });
        break;

      case ArbitrageStep.TRANSFER_TO_CEX:
        console.log("🔄 重新执行: 将 IOTX 充值到 CEX");
        const depositAddress = await this.cexService.getIotxDepositAddress();
        const transferHash = await this.mimoService.sendIotx(
          state.data.swapResult.iotxAmount,
          from(depositAddress).stringEth()
        );
        console.log(`💸 IOTX 转账哈希: ${transferHash}`);
        await this.cexService.waitForIotxBalanceIncrease(state.data.swapResult.iotxAmount);
        await this.updateStep(state.id, ArbitrageStep.SELL_TOKEN);
        break;

      case ArbitrageStep.SELL_TOKEN:
        console.log("🔄 重新执行: 在 CEX 卖出 IOTX");
        const sellOrder = await this.cexService.sellIotx(state.data.swapResult.iotxAmount);
        await this.updateStep(state.id, ArbitrageStep.COMPLETED, { sellOrder });
        break;

      default:
        throw new Error(`Unknown step for DEX->CEX strategy: ${state.currentStep}`);
    }

    // 如果还没完成，继续下一步
    const updatedState = await this.getState(state.id);
    if (updatedState && updatedState.currentStep !== ArbitrageStep.COMPLETED) {
      await this.resumeSolPath(updatedState);
    }
  }

  /**
   * 等待 IoTeX 上的 SOL 余额增加
   * @param expectedAmount 预期增加的 SOL 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 5 分钟
   * @param checkInterval 检查间隔（毫秒），默认 10 秒
   */
  private async waitForSolBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 600000, // 10 分钟 (跨链转账需要更长时间)
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 IoTeX SOL 余额增加 ${expectedAmount} SOL...`);

    // 获取初始余额
    let initialBalance: number;
    try {
      initialBalance = parseFloat(
        await this.mimoService.getTokenBalanceFormatted("SOL")
      );
      console.log(`📊 初始 IoTeX SOL 余额: ${initialBalance} SOL`);
    } catch (error) {
      console.error("获取初始 IoTeX SOL 余额失败:", error);
      throw new Error("无法获取 IoTeX SOL 余额，请检查网络连接");
    }

    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%
    console.log(`🎯 目标余额: ${targetBalance.toFixed(6)} SOL (需增加 ${(expectedAmount * 0.95).toFixed(6)} SOL)`);

    let checkCount = 0;
    while (Date.now() - startTime < maxWaitTime) {
      try {
        checkCount++;
        const currentBalance = parseFloat(
          await this.mimoService.getTokenBalanceFormatted("SOL")
        );
        const elapsedTime = Math.floor((Date.now() - startTime) / 1000);

        // 每 3 次检查才输出一次日志，避免过多输出
        if (checkCount % 3 === 1) {
          console.log(`🔍 检查 ${checkCount} (${elapsedTime}s): 当前余额 ${currentBalance} SOL`);
        }

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialBalance;
          console.log(`✅ IoTeX SOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 IoTeX SOL 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    console.log("⏰ 达到最大等待时间，进行最终检查...");
    const finalBalance = parseFloat(
      await this.mimoService.getTokenBalanceFormatted("SOL")
    );
    const actualIncrease = finalBalance - initialBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到余额增加 ${actualIncrease.toFixed(6)} SOL，继续执行...`);
    } else {
      console.log(`❌ 等待 ${maxWaitTime / 1000} 秒后仍未检测到余额变化`);
      throw new Error(`等待 IoTeX SOL 到账超时，预期增加 ${expectedAmount} SOL，实际增加 ${actualIncrease.toFixed(6)} SOL`);
    }
  }

  /**
   * 等待 IoTeX 上的 IOTX 余额增加
   * @param expectedAmount 预期增加的 IOTX 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 3 分钟
   * @param checkInterval 检查间隔（毫秒），默认 10 秒
   */
  private async waitForIotxBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 180000, // 3 分钟
    checkInterval: number = 10000 // 10 秒
  ): Promise<void> {
    console.log(`⏳ 等待 IoTeX IOTX 余额增加 ${expectedAmount} IOTX...`);

    // 获取初始余额
    const initialBalance = parseFloat(
      await this.mimoService.getTokenBalanceFormatted("IOTX")
    );

    const startTime = Date.now();
    const targetBalance = initialBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const currentBalance = parseFloat(
          await this.mimoService.getTokenBalanceFormatted("IOTX")
        );

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialBalance;
          console.log(`✅ IOTX 余额已增加 ${actualIncrease.toFixed(2)} IOTX`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 IOTX 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    const finalBalance = parseFloat(
      await this.mimoService.getTokenBalanceFormatted("IOTX")
    );
    const actualIncrease = finalBalance - initialBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到余额增加 ${actualIncrease.toFixed(2)} IOTX，继续执行...`);
    } else {
      throw new Error(`等待 IOTX 到账超时，预期增加 ${expectedAmount} IOTX，实际增加 ${actualIncrease.toFixed(2)} IOTX`);
    }
  }

  /**
   * 等待 Solana 上的 WSOL 余额增加（跨链转账接收的是 WSOL）
   * @param expectedAmount 预期增加的 WSOL 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 10 分钟
   * @param checkInterval 检查间隔（毫秒），默认 15 秒
   */
  private async waitForSolanaWsolBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 600000, // 10 分钟 (跨链转账需要更长时间)
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 Solana WSOL 余额增加 ${expectedAmount} SOL...`);

    // 获取初始 WSOL 余额（跨链转账接收的是 WSOL，不是 native SOL）
    let initialWsolBalance: number;
    let initialNativeBalance: number;
    try {
      const balances = await this.solanaService.getTotalSolBalance();
      initialWsolBalance = balances.wsol;
      initialNativeBalance = balances.native;
      console.log(`📊 初始 Solana 余额: Native SOL: ${initialNativeBalance} SOL, WSOL: ${initialWsolBalance} SOL`);
    } catch (error) {
      console.error("获取初始 Solana 余额失败:", error);
      throw new Error("无法获取 Solana 余额，请检查网络连接");
    }

    const startTime = Date.now();
    const targetWsolBalance = initialWsolBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%
    console.log(`🎯 目标 WSOL 余额: ${targetWsolBalance.toFixed(6)} SOL (需增加 ${(expectedAmount * 0.95).toFixed(6)} SOL)`);

    let checkCount = 0;
    while (Date.now() - startTime < maxWaitTime) {
      try {
        checkCount++;
        const currentBalances = await this.solanaService.getTotalSolBalance();
        const elapsedTime = Math.floor((Date.now() - startTime) / 1000);

        console.log(`🔍 第 ${checkCount} 次检查 (${elapsedTime}s): Native: ${currentBalances.native} SOL, WSOL: ${currentBalances.wsol} SOL`);

        if (currentBalances.wsol >= targetWsolBalance) {
          const actualIncrease = currentBalances.wsol - initialWsolBalance;
          console.log(`✅ Solana WSOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
          return;
        }

        const remainingTime = Math.floor((maxWaitTime - (Date.now() - startTime)) / 1000);
        console.log(`⏳ 余额未达到目标，${checkInterval / 1000} 秒后重新检查 (剩余 ${remainingTime}s)`);

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 Solana SOL 余额时出错:", error);
        console.log(`⏳ 网络错误，${checkInterval / 1000} 秒后重试...`);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    console.log("⏰ 达到最大等待时间，进行最终检查...");
    let finalBalances: { native: number; wsol: number; total: number };
    try {
      finalBalances = await this.solanaService.getTotalSolBalance();
    } catch (error) {
      console.error("最终余额检查失败:", error);
      throw new Error("无法完成最终余额检查，请手动验证转账状态");
    }

    const actualWsolIncrease = finalBalances.wsol - initialWsolBalance;
    const actualNativeIncrease = finalBalances.native - initialNativeBalance;
    console.log(`📊 最终余额: Native: ${finalBalances.native} SOL, WSOL: ${finalBalances.wsol} SOL`);
    console.log(`📊 余额变化: Native: ${actualNativeIncrease.toFixed(6)} SOL, WSOL: ${actualWsolIncrease.toFixed(6)} SOL`);

    if (actualWsolIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到 WSOL 余额增加 ${actualWsolIncrease.toFixed(6)} SOL，继续执行...`);
    } else {
      console.log(`❌ 等待 ${maxWaitTime / 1000} 秒后仍未检测到 WSOL 余额变化`);
      console.log(`💡 建议: 请手动检查 Solana 地址 ${this.config.walletAddresses.solana} 的 WSOL 余额`);
      throw new Error(`等待 Solana WSOL 到账超时，预期增加 ${expectedAmount} SOL，实际增加 ${actualWsolIncrease.toFixed(6)} SOL`);
    }
  }

  /**
   * 等待 Solana 上的 native SOL 余额增加（CEX 提现接收的是 native SOL）
   * @param expectedAmount 预期增加的 native SOL 数量
   * @param maxWaitTime 最大等待时间（毫秒），默认 5 分钟
   * @param checkInterval 检查间隔（毫秒），默认 15 秒
   */
  private async waitForSolanaBalanceIncrease(
    expectedAmount: number,
    maxWaitTime: number = 300000, // 5 分钟 (CEX 提现相对较快)
    checkInterval: number = 15000 // 15 秒
  ): Promise<void> {
    console.log(`⏳ 等待 Solana native SOL 余额增加 ${expectedAmount} SOL...`);

    // 获取初始 native SOL 余额
    let initialNativeBalance: number;
    try {
      initialNativeBalance = await this.solanaService.getSolBalance();
      console.log(`📊 初始 Solana native SOL 余额: ${initialNativeBalance} SOL`);
    } catch (error) {
      console.error("获取初始 Solana native SOL 余额失败:", error);
      throw new Error("无法获取 Solana native SOL 余额，请检查网络连接");
    }

    const startTime = Date.now();
    const targetBalance = initialNativeBalance + expectedAmount * 0.95; // 考虑到可能的手续费损失，设置为 95%
    console.log(`🎯 目标 native SOL 余额: ${targetBalance.toFixed(6)} SOL (需增加 ${(expectedAmount * 0.95).toFixed(6)} SOL)`);

    let checkCount = 0;
    while (Date.now() - startTime < maxWaitTime) {
      try {
        checkCount++;
        const currentBalance = await this.solanaService.getSolBalance();
        const elapsedTime = Math.floor((Date.now() - startTime) / 1000);

        // 每 3 次检查才输出一次日志，避免过多输出
        if (checkCount % 3 === 1) {
          console.log(`🔍 检查 ${checkCount} (${elapsedTime}s): 当前 native SOL 余额 ${currentBalance} SOL`);
        }

        if (currentBalance >= targetBalance) {
          const actualIncrease = currentBalance - initialNativeBalance;
          console.log(`✅ Solana native SOL 余额已增加 ${actualIncrease.toFixed(6)} SOL`);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      } catch (error) {
        console.error("检查 Solana native SOL 余额时出错:", error);
        await new Promise((resolve) => setTimeout(resolve, checkInterval));
      }
    }

    // 超时后的最终检查
    console.log("⏰ 达到最大等待时间，进行最终检查...");
    const finalBalance = await this.solanaService.getSolBalance();
    const actualIncrease = finalBalance - initialNativeBalance;

    if (actualIncrease > 0) {
      console.log(`⚠️ 等待超时，但检测到 native SOL 余额增加 ${actualIncrease.toFixed(6)} SOL，继续执行...`);
    } else {
      console.log(`❌ 等待 ${maxWaitTime / 1000} 秒后仍未检测到 native SOL 余额变化`);
      throw new Error(`等待 Solana native SOL 到账超时，预期增加 ${expectedAmount} SOL，实际增加 ${actualIncrease.toFixed(6)} SOL`);
    }
  }

  /**
   * 执行套利策略：CEX -> DEX
   * CEX 价格高于 DEX 价格时执行
   */
  async execIotexPath(usdtAmount: number) {
    console.log(`🚀 执行 CEX -> DEX 套利策略，金额: $${usdtAmount}`);

    // 先检查是否有未完成的同类型流程
    await this.checkAndRecoverProcesses();

    // 创建新的流程状态
    const processId = this.generateProcessId();
    const state: ArbitrageState = {
      id: processId,
      strategy: ArbitrageStrategy.CEX_TO_DEX,
      currentStep: ArbitrageStep.INIT,
      usdtAmount,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      data: {},
      retryCount: 0,
      maxRetries: 3
    };

    try {
      // 记录初始钱包余额
      const initialBalances = {
        solOnChain: await this.solanaService.getSolBalance(),
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted(
          "IOTX",
          this.mimoService.getWalletAddress()
        ),
      };

      await this.updateStep(processId, ArbitrageStep.BUY_TOKEN, { initialBalances });

      // 1. 在 CEX 上买 IOTX
      console.log("步骤 1: 在 CEX 上买 IOTX");
      const buyOrder = await this.cexService.buyIotx(usdtAmount);
      let iotxAmount = buyOrder.filled || buyOrder.amount;
      iotxAmount = Math.floor(iotxAmount);
      await this.updateStep(processId, ArbitrageStep.WITHDRAW_TOKEN, { buyOrder });

      // 短暂等待确保订单处理完成
      await new Promise((resolve) => setTimeout(resolve, 10000));

      // 2. 将 IOTX 提现到 IoTeX 钱包
      console.log("步骤 2: 将 IOTX 提现到 IoTeX 钱包");
      await this.cexService.withdrawIotx(
        iotxAmount,
        this.config.walletAddresses.iotex
      );
      await this.waitForIotxBalanceIncrease(iotxAmount);
      await this.updateStep(processId, ArbitrageStep.SWAP_TOKEN);

      // 3. 在 MIMO 将 IOTX 换成 SOL
      console.log("步骤 3: 在 MIMO 将 IOTX 换成 SOL");
      const swapResult = await this.mimoService.swapIotxToSol(iotxAmount);
      await this.updateStep(processId, ArbitrageStep.BRIDGE_TRANSFER, { swapResult });

      // 4. 将 SOL 通过跨链桥转移到 Solana
      console.log("步骤 4: 将 SOL 通过跨链桥转移到 Solana");
      const bridgeResult =
        await this.bridgeService.transferSolFromIotexToSolana(
          swapResult.solAmount,
          this.config.walletAddresses.solana
        );
      await this.waitForSolanaWsolBalanceIncrease(bridgeResult.receivedAmount, 1000 * 60 * 10);
      await this.updateStep(processId, ArbitrageStep.UNWRAP_SOL, { bridgeResult });

      const solanaService = new SolanaService({
        rpcUrl: this.bridgeService.config.solanaRpcUrl,
        privateKey: this.bridgeService.config.solanaPrivateKey,
      })

      // 5. 解包 wSOL 并转账到 CEX
      console.log("步骤 5: 解包 wSOL 并转账到 CEX");
      await solanaService.unwrapSol();
      await this.updateStep(processId, ArbitrageStep.TRANSFER_TO_CEX);

      const bnDepositSolAddress = await this.cexService.getSolDepositAddress();
      const transferTx = await solanaService.transfer(bnDepositSolAddress, swapResult.solAmount);
      console.log(`💸 SOL 转账哈希: ${transferTx}`);

      await this.cexService.waitForSolBalanceIncrease(swapResult.solAmount);
      await this.updateStep(processId, ArbitrageStep.SELL_TOKEN);

      // 6. 在 CEX 卖出 SOL
      console.log("步骤 6: 在 CEX 卖出 SOL");
      await this.cexService.sellSymbol("SOLUSDT", swapResult.solAmount);
      await this.updateStep(processId, ArbitrageStep.COMPLETED);

      // 记录最终钱包余额
      const finalBalances = {
        solOnChain: await this.solanaService.getSolBalance(),
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted(
          "IOTX",
          this.mimoService.getWalletAddress()
        ),
      };

      // 更新最终状态
      await this.updateStep(processId, ArbitrageStep.COMPLETED, { finalBalances });

      console.log("📊 最终钱包余额:", {
        "SOL (链上)": `${finalBalances.solOnChain} SOL`,
        "IOTX (链上)": `${finalBalances.iotxOnChain} IOTX`,
      });

      // 计算钱包余额变化
      const balanceChanges = {
        solOnChain: finalBalances.solOnChain - initialBalances.solOnChain,
        iotxOnChain:
          parseFloat(finalBalances.iotxOnChain) -
          parseFloat(initialBalances.iotxOnChain),
      };

      // 余额变化已计算，用于后续盈利分析

      // 计算盈利（注意：CEX到DEX策略目前未完成最后的卖出步骤）
      const totalCost =
        buyOrder.cost || buyOrder.filled * buyOrder.average! || usdtAmount; // 实际成交金额
      const currentSolPrice = await this.cexService.getSolPrice();
      const currentIotxPrice = await this.cexService.getIotxPrice();
      const estimatedSolValue = bridgeResult.receivedAmount * currentSolPrice; // 估算最终SOL价值
      const tradingNetProfit = estimatedSolValue - totalCost;
      const tradingProfitPercentage = (tradingNetProfit / totalCost) * 100;

      // 基于钱包余额变化计算实际净盈利
      const actualNetProfit =
        balanceChanges.solOnChain * currentSolPrice +
        balanceChanges.iotxOnChain * currentIotxPrice;
      const actualProfitPercentage =
        totalCost > 0 ? (actualNetProfit / totalCost) * 100 : 0;

      const result = {
        strategy: "cex_to_dex",
        initialAmount: usdtAmount,
        buyOrder,
        swapResult,
        bridgeResult,
        // 盈利分析（估算值，因为未完成最终卖出）
        profitAnalysis: {
          // 基于交易记录的分析
          tradingCost: totalCost,
          tradingRevenue: estimatedSolValue,
          tradingNetProfit,
          tradingProfitPercentage,
          // 基于钱包余额变化的分析（更准确）
          initialBalances,
          finalBalances,
          balanceChanges,
          actualNetProfit,
          actualProfitPercentage,
          fees: {
            buyOrderFee: buyOrder.fee?.cost || 0,
            bridgeFee: bridgeResult.bridgeFee || 0,
            swapFee: parseFloat(swapResult.priceImpact) || 0,
          },
        },
        note: "盈利为估算值，需要手动完成SOL卖出步骤",
        status: "completed",
      };

      console.log(`💰 套利完成 - 投入: $${totalCost} | 估算盈利: $${tradingNetProfit.toFixed(2)} (${tradingProfitPercentage.toFixed(2)}%)`);

      // 从活跃流程列表中移除
      await redisClient.srem(this.ACTIVE_PROCESSES_KEY, processId);

      return result;
    } catch (error) {
      console.error("❌ CEX -> DEX 套利失败:", error);

      // 标记流程为失败
      try {
        await this.updateStep(processId, ArbitrageStep.FAILED, null, error instanceof Error ? error.message : String(error));
      } catch (updateError) {
        console.error("更新失败状态时出错:", updateError);
      }

      throw error;
    }
  }

  /**
   * 执行套利策略：DEX -> CEX
   * DEX 价格高于 CEX 价格时执行
   */
  async execSolPath(usdtAmount: number) {
    console.log(`🚀 执行 DEX -> CEX 套利策略，金额: $${usdtAmount}`);

    // 先检查是否有未完成的同类型流程
    await this.checkAndRecoverProcesses();

    // 创建新的流程状态
    const processId = this.generateProcessId();
    const state: ArbitrageState = {
      id: processId,
      strategy: ArbitrageStrategy.DEX_TO_CEX,
      currentStep: ArbitrageStep.INIT,
      usdtAmount,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      data: {},
      retryCount: 0,
      maxRetries: 3
    };

    try {
      // 记录初始链上钱包余额
      const initialBalances = {
        solOnChain: await this.solanaService.getSolBalance(), // Solana 链上 SOL 余额
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted("IOTX"), // IoTeX 链上 IOTX 余额
      };

      await this.updateStep(processId, ArbitrageStep.BUY_TOKEN, { initialBalances });
      // 预检查：计算能买到的 SOL 数量是否满足最小提现要求
      const minWithdrawAmount = 0.1;
      const solPrice = await this.cexService.getSolPrice();
      const estimatedSolAmount = usdtAmount / solPrice;

      if (estimatedSolAmount < minWithdrawAmount) {
        throw new Error(
          `预估 SOL 购买数量 ${estimatedSolAmount.toFixed(
            4
          )} 小于币安最小提现要求 ${minWithdrawAmount} SOL，需要至少 $${(
            minWithdrawAmount * solPrice
          ).toFixed(2)} USDT`
        );
      }

      // 1. 在 CEX 上买 SOL
      console.log("步骤 1: 在 CEX 上买 SOL");
      const buyOrder = await this.cexService.buySol(usdtAmount);
      const solAmount = buyOrder.filled || buyOrder.amount;
      await this.updateStep(processId, ArbitrageStep.WITHDRAW_TOKEN, { buyOrder });

      // 二次检查实际买入金额
      if (solAmount < minWithdrawAmount) {
        throw new Error(
          `实际 SOL 购买数量 ${solAmount} 小于币安最小提现要求 ${minWithdrawAmount} SOL`
        );
      }

      // 短暂等待确保订单处理完成
      await new Promise((resolve) => setTimeout(resolve, 5_000));

      // 2. 将 SOL 提现到 Solana 钱包
      console.log("步骤 2: 将 SOL 提现到 Solana 钱包");
      await this.cexService.withdrawSol(
        solAmount,
        this.config.walletAddresses.solana
      );
      await this.waitForSolanaBalanceIncrease(solAmount);
      await this.updateStep(processId, ArbitrageStep.BRIDGE_TRANSFER);

      // 3. 通过跨链桥将 SOL 转移到 IoTeX
      console.log("步骤 3: 通过跨链桥将 SOL 转移到 IoTeX");
      const bridgeResult =
        await this.bridgeService.transferSolFromSolanaToIotex(
          solAmount,
          this.config.walletAddresses.iotex
        );
      await this.waitForSolBalanceIncrease(bridgeResult.receivedAmount);
      await this.updateStep(processId, ArbitrageStep.SWAP_TOKEN, { bridgeResult });

      // 4. 在 MIMO 上将 SOL 转换为 IOTX
      console.log("步骤 4: 在 MIMO 上将 SOL 转换为 IOTX");
      const swapResult = await this.mimoService.swapSolToIotx(
        bridgeResult.receivedAmount
      );
      await this.updateStep(processId, ArbitrageStep.TRANSFER_TO_CEX, { swapResult });

      // 5. 将 IOTX 充值到 CEX 并卖出获得 USDT
      console.log("步骤 5: 将 IOTX 充值到 CEX");
      const depositAddress = await this.cexService.getIotxDepositAddress();
      const transferHash = await this.mimoService.sendIotx(
        swapResult.iotxAmount,
        from(depositAddress).stringEth()
      );
      console.log(`💸 IOTX 转账哈希: ${transferHash}`);

      await this.cexService.waitForIotxBalanceIncrease(swapResult.iotxAmount);
      await this.updateStep(processId, ArbitrageStep.SELL_TOKEN);

      // 6. 在 CEX 卖出 IOTX
      console.log("步骤 6: 在 CEX 卖出 IOTX");
      const sellOrder = await this.cexService.sellIotx(swapResult.iotxAmount);
      await this.updateStep(processId, ArbitrageStep.COMPLETED, { sellOrder });

      // 记录最终链上钱包余额
      const finalBalances = {
        solOnChain: await this.solanaService.getSolBalance(), // Solana 链上 SOL 余额
        iotxOnChain: await this.mimoService.getTokenBalanceFormatted("IOTX"), // IoTeX 链上 IOTX 余额
      };

      // 更新最终状态
      await this.updateStep(processId, ArbitrageStep.COMPLETED, { finalBalances });
      // 最终余额已记录，用于后续分析

      // 计算钱包余额变化
      const balanceChanges = {
        solOnChain: finalBalances.solOnChain - initialBalances.solOnChain,
        iotxOnChain:
          parseFloat(finalBalances.iotxOnChain) -
          parseFloat(initialBalances.iotxOnChain),
      };

      // 计算盈利（基于交易记录）
      const totalCost =
        buyOrder.cost || buyOrder.filled * buyOrder.average! || usdtAmount; // 实际成交金额
      const totalRevenue = sellOrder.filled
        ? sellOrder.filled * sellOrder.average!
        : sellOrder.cost || 0; // 卖出获得的USDT
      const tradingNetProfit = totalRevenue - totalCost;
      const tradingProfitPercentage = (tradingNetProfit / totalCost) * 100;

      // 计算实际钱包净变化（更准确的盈利计算）
      const actualNetProfit =
        balanceChanges.solOnChain * solPrice +
        balanceChanges.iotxOnChain * (await this.cexService.getIotxPrice());
      const actualProfitPercentage = (actualNetProfit / usdtAmount) * 100;

      const result = {
        strategy: "dex_to_cex",
        initialAmount: usdtAmount,
        buyOrder,
        bridgeResult,
        swapResult,
        sellOrder,
        // 盈利分析
        profitAnalysis: {
          // 基于交易记录的分析
          tradingCost: totalCost,
          tradingRevenue: totalRevenue,
          tradingNetProfit,
          tradingProfitPercentage,
          // 基于钱包余额变化的分析（更准确）
          initialBalances,
          finalBalances,
          balanceChanges,
          actualNetProfit,
          actualProfitPercentage,
          fees: {
            buyOrderFee: buyOrder.fee?.cost || 0,
            sellOrderFee: sellOrder.fee?.cost || 0,
            bridgeFee: bridgeResult.bridgeFee || 0,
            swapFee: parseFloat(swapResult.priceImpact) || 0,
          },
        },
        status: "completed",
      };

      console.log(`💰 套利完成 - 投入: $${totalCost} | 收入: $${totalRevenue.toFixed(2)} | 盈利: $${tradingNetProfit.toFixed(2)} (${tradingProfitPercentage.toFixed(2)}%)`);

      // 从活跃流程列表中移除
      await redisClient.srem(this.ACTIVE_PROCESSES_KEY, processId);

      return result;
    } catch (error) {
      console.error("❌ DEX -> CEX 套利失败:", error);

      // 标记流程为失败
      try {
        await this.updateStep(processId, ArbitrageStep.FAILED, null, error instanceof Error ? error.message : String(error));
      } catch (updateError) {
        console.error("更新失败状态时出错:", updateError);
      }

      throw error;
    }
  }
}
